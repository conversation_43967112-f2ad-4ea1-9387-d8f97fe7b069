<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f5f5f5">

    <!-- Service List Table -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="15dp"
        android:layout_marginBottom="20dp"
        android:elevation="2dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Service List"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="15dp"
            android:layout_gravity="center" />

        <!-- Table Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="#e9ecef"
            android:padding="8dp">

            <TextView
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_height="wrap_content"
                android:text="#"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="Service No"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="2"
                android:layout_height="wrap_content"
                android:text="Service Name"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1.5"
                android:layout_height="wrap_content"
                android:text="Type"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="Price ($)"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="Action"
                android:textStyle="bold"
                android:textSize="12sp"
                android:gravity="center" />

        </LinearLayout>

        <!-- RecyclerView for service list -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewServices"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp" />

    </LinearLayout>

    <!-- Create New Service Button -->
    <Button
        android:id="@+id/btnCreateNewService"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Create New Service"
        android:background="#28a745"
        android:textColor="#ffffff"
        android:padding="15dp"
        android:textSize="16sp"
        android:layout_marginBottom="20dp" />

    <!-- Business Rules Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="15dp"
        android:elevation="2dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Business rules"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="10dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1. Khi người dùng nhấp vào nút 'Save', dữ liệu sẽ được thêm (validate) và lưu vào cơ sở dữ liệu DB."
            android:textSize="12sp"
            android:layout_marginBottom="5dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2. Nếu dữ liệu không hợp lệ, hiển thị thông báo lỗi và không lưu vào cơ sở dữ liệu."
            android:textSize="12sp"
            android:layout_marginBottom="5dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3. Lưu dữ liệu vào DB"
            android:textSize="12sp"
            android:layout_marginBottom="5dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="4. Hiển thị thông báo cho người dùng"
            android:textSize="12sp"
            android:layout_marginBottom="5dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="5. Và có thể thực hiện đồng thời nhập và ngăn chặn người dùng có thể nhập mới trong Screen Definition."
            android:textSize="12sp"
            android:layout_marginBottom="5dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="6. Dữ liệu người dùng nhập vào có thể được validate theo yêu cầu của bài toán và luôn thể quy tắc sẽ lên của Java và Android"
            android:textSize="12sp"
            android:layout_marginBottom="5dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="7. Khi người dùng nhấp vào nút 'Back', chuyển đến quy tắc lại màn hình Service List và đánh số thứ tự theo dữ liệu mới nhất trong cơ sở dữ liệu"
            android:textSize="12sp" />

    </LinearLayout>

</LinearLayout>
