@echo off
echo ========================================
echo    KS1803-HE176172 Service Management App
echo ========================================
echo.

echo [1] Building APK...
call gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo [2] APK built successfully!
echo Location: app\build\outputs\apk\debug\app-debug.apk
echo File size:
dir app\build\outputs\apk\debug\app-debug.apk | find "app-debug.apk"
echo.

echo [3] Checking for connected devices...
where adb >nul 2>nul
if %errorlevel% neq 0 (
    echo ADB not found in PATH. Trying Android SDK location...
    set "ADB_PATH=%LOCALAPPDATA%\Android\Sdk\platform-tools\adb.exe"
    if exist "%ADB_PATH%" (
        "%ADB_PATH%" devices
    ) else (
        echo ADB not found. Please install Android SDK or connect device manually.
        goto manual_install
    )
) else (
    adb devices
)
echo.

echo [4] Attempting to install APK...
if exist "%ADB_PATH%" (
    "%ADB_PATH%" install -r app\build\outputs\apk\debug\app-debug.apk
    set install_result=%errorlevel%
) else (
    adb install -r app\build\outputs\apk\debug\app-debug.apk
    set install_result=%errorlevel%
)

if %install_result% equ 0 (
    echo.
    echo [5] App installed successfully!
    echo [6] Starting app...
    if exist "%ADB_PATH%" (
        "%ADB_PATH%" shell am start -n com.example.ks1803_he176172_servicemgtapp/.MainActivity
    ) else (
        adb shell am start -n com.example.ks1803_he176172_servicemgtapp/.MainActivity
    )
    echo.
    echo App is now running on your device!
) else (
    :manual_install
    echo.
    echo Installation failed or no device connected.
    echo.
    echo MANUAL INSTALLATION INSTRUCTIONS:
    echo 1. Copy the APK file: app\build\outputs\apk\debug\app-debug.apk
    echo 2. Transfer it to your Android device via USB/Email/Cloud
    echo 3. On your device: Settings → Security → Enable "Unknown Sources"
    echo 4. Open the APK file on your device to install
    echo 5. Launch "Service Management App" from app drawer
    echo.
    echo APK is ready for manual installation!
)

echo.
echo ========================================
echo Press any key to exit...
pause >nul
